.thinkContainer {
  // margin-bottom: 12px;
}

.thinkTitle {
  width: 180px;
  //   margin-bottom: 10px;
  padding: 8px 10px;
  border-radius: 8px;
  border: 1px solid var(--primary-background-color-5);
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 13px;
  color: #5f606a;
  cursor: pointer;
}

.thinkArrow {
  margin-right: 6px;
  transition: transform 0.3s ease;
  display: inline-block;
  width: 16px;
  height: 16px;

  img {
    width: 100%;
    height: 100%;
    display: block;
    transition: transform 0.3s ease;
    transform: rotate(-90deg);
  }

  &.collapsed img {
    transform: rotate(90deg);
  }
}

.thinkContentContainer {
  padding: 10px;
  line-height: 22px;
  border-radius: 8px;
  font-size: 13px;
  color: #74757e;
  transition: all 0.3s ease;

  &.collapsed {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-bottom: 0;
    opacity: 0;
  }

  &.expanded {
    max-height: 1000px;
    overflow: auto;
    opacity: 1;
  }

  :global {
    .linkflow-steps-item-content {
      .linkflow-steps-item-title {
        font-size: 13px;
        color: #74757e;
      }

      min-height: 25px !important;
    }
  }
}
