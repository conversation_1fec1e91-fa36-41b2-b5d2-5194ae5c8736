import React, { FC, useState } from 'react';
import thinkIcon from '@/assets/stream/think.png';
import arrowIcon from '@/assets/stream/arrow.png';
import { Space, Steps } from '@ht/sprite-ui';
import type { StepsProps } from '@ht/sprite-ui';
import { LoadingOutlined } from '@ht-icons/sprite-ui-react';
import check from '@/assets/stream/check.png';

import styles from './index.less';

const { Step } = Steps;

interface ThinkContentProps {
  content: {
    think?: {
      answer: string;
    };
    plugins: PluginCallProps[];
  };
}

interface PluginCallProps {
  clientMsgId: string;
  conversation_id: string;
  created_at: number;
  event: string;
  index: number;
  latency?: number;
  name: string;
  operation_id: string;
  status?: number;
  trace_id: string;
  start?: number;
  end?: number;
  answer?: string;
}

const customDot: StepsProps['progressDot'] = (dot, { status, index }) => {
  if (status === 'finish') {
    return (
      <img
        style={{
          width: '18px',
          height: '18px',
          position: 'relative',
          top: '-7px',
          left: '-5px',
        }}
        src={check}
      />
    );
  } else if (status === 'wait') {
    return (
      <LoadingOutlined
        style={{
          width: '18px',
          height: '18px',
          position: 'relative',
          top: '-5px',
          left: '-5px',
        }}
      />
    );
  }
};

const ThinkContent: FC<ThinkContentProps> = ({ content }) => {
  console.log('渲染ThinkContent', content);
  const thinkText = content?.think?.answer;
  const plugins = content?.plugins;
  const [isExpanded, setIsExpanded] = useState(true);

  if (!thinkText && !plugins?.length) {
    return;
  }

  return (
    <div className={styles.thinkContainer}>
      <div
        className={styles.thinkTitle}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <img src={thinkIcon}></img>
        思考和规划过程
        <span
          className={
            isExpanded
              ? styles.thinkArrow
              : `${styles.thinkArrow} ${styles.collapsed}`
          }
        >
          <img src={arrowIcon} alt="arrow" />
        </span>
      </div>
      <div
        className={`${styles.thinkContentContainer} ${
          isExpanded ? styles.expanded : styles.collapsed
        }`}
      >
        {content?.plugins && (
          <Steps
            progressDot={customDot}
            direction="vertical"
            current={content?.plugins?.length + 1}
          >
            {content.plugins.map((data: PluginCallProps) => {
              let time = 0;
              const title =
                data.name + (data.latency ? ` - ${data.latency}s` : '');
              const description = '';
              let status = 'wait';
              if (data.clientMsgId) {
                // 是过程渲染状态
                time = data.created_at * 1000;
                // description = data.event;
                status = data?.event?.endsWith('_end') ? 'finish' : 'wait';
              } else {
                // 是结果渲染状态

                time = (data.end || 0) * 1000;
                // description = data.answer;
                status = data.status === 0 ? 'finish' : 'wait';
              }
              return (
                <Step
                  title={`${new Date(time).toLocaleString()} - ${title}`}
                  key={time}
                  // description={description}
                  status={status}
                />
              );
            })}
          </Steps>
        )}
        {thinkText}
      </div>
    </div>
  );
};

export default ThinkContent;
