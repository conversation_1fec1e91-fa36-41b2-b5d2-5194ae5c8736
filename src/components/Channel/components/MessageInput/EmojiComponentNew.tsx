/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-lines */
/* eslint-disable indent */
import { FC, useEffect, useRef, useState } from 'react';
import { Dropdown, message, Popover, message as Message } from '@ht/sprite-ui';
import favourite from '@/assets/channel/messageInput/favourite.png';
import goLeftIcon from '@/assets/channel/messageInput/goLeftIcon.svg';
import goLeftValidIcon from '@/assets/channel/messageInput/goLeftValidIcon.svg';
import goRightIcon from '@/assets/channel/messageInput/goRightIcon.svg';
import goRightValidIcon from '@/assets/channel/messageInput/goRightValidIcon.svg';
import faceLoading from '@/assets/channel/messageInput/faceLoading.svg';
import data from '@emoji-mart/data/sets/15/apple.json';
import addEmoji from '@/assets/channel/messageInput/addEmoji.svg';
import { IMSDK } from '@/layouts/BasicLayout';
import Upload from '@ht/sprite-ui/lib/upload/Upload';

import { useSendMessage } from '@/hooks/useSendMessage';
import { useConversationStore } from '@/store/conversation';
import classNames from 'classnames';
import {
  UserFace,
  SysFace,
  SysFaceGroupInfo,
} from '@ht/openim-wasm-client-sdk';
import emitter from '@/utils/events';
import { useFileMessage } from '@/hooks/useFileMessage';
import Tooltip from '@ht/sprite-ui/es/tooltip';
import styles from './index.less';
import ImageWithLoading from './ImageWithLoading';

interface EmojiComponentProps {
  handleEmojiSelect: (val: any) => void;
  disabledBtn: boolean;
  item: any;
}

const MAX_VISIBLE = 7; // 最多展示的表情包的Tab数
const APPLE_EMOJI_TAB_KEY = 'appleEmoji'; // 'appleEmoji' key值

const APPLE_EMOJI_TAB = {
  faceGroupID: APPLE_EMOJI_TAB_KEY,
  faceGroupNo: 0,
  faceGroupName: 'appleEmoji',
  faceGroupIcon: '🙂',
  createTime: 0,
};

const MY_FAVOURITE_TAB_KEY = 'favourite'; // '我的收藏' key值

const MY_FAVOURITE_TAB = {
  faceGroupID: MY_FAVOURITE_TAB_KEY,
  faceGroupNo: 0,
  faceGroupName: '我的收藏',
  faceGroupIcon: favourite,
  createTime: 0,
};

const EmojiComponentNew: FC<EmojiComponentProps> = ({
  handleEmojiSelect,
  item,
  disabledBtn = false,
}) => {
  const [open, setOpen] = useState<boolean>(false);
  const popRef = useRef<HTMLDivElement>(null);
  const currentConversation = useConversationStore(
    (state) => state.currentConversation
  );
  const currentMultiSession = useConversationStore(
    (state) => state.currentMultiSession
  );
  const isMultiSession = useConversationStore((state) => state.isMultiSession);

  const { sendMessage } = useSendMessage(
    isMultiSession ? currentMultiSession : currentConversation
  );

  const [isToolTipOpen, setIsToolTipOpen] = useState<boolean>(false);

  const [selectedGroupID, setSelectedGroupID] =
    useState<string>(MY_FAVOURITE_TAB_KEY); // 当前选中的表情包，默认选中自定义
  const [faceGroups, setFaceGroups] = useState<SysFaceGroupInfo[]>([
    APPLE_EMOJI_TAB,
    MY_FAVOURITE_TAB,
  ]);

  const emojis = [
    'smile',
    'smiley',
    'grinning',
    'blush',
    'wink',
    'heart_eyes',
    'kissing_heart',
    'kissing_closed_eyes',
    'flushed',
    'relieved',
    'grin',
    'stuck_out_tongue_winking_eye',
    'stuck_out_tongue_closed_eyes',
    'unamused',
    'smirk',
    'sweat',
    'pensive',
    'disappointed',
    'confounded',
    'disappointed_relieved',
    'cold_sweat',
    'fearful',
    'persevere',
    'cry',
    'sob',
    'joy',
    'astonished',
    'scream',
    'angry',
    'rage',
    'sleepy',
    'mask',
    'imp',
    'alien',
    'yellow_heart',
    'blue_heart',
    'purple_heart',
    'heartpulse',
    'green_heart',
    'heart',
    'broken_heart',
    'heartbeat',
    'cupid',
    'sparkles',
    'star2',
    'anger',
    'grey_exclamation',
    'grey_question',
    'zzz',
    'dash',
    'sweat_drops',
    'notes',
    'musical_note',
    'fire',
    'hankey',
    '+1',
    '-1',
    'ok_hand',
    'facepunch',
    'fist',
    'v',
    'wave',
    'hand',
    'open_hands',
    'point_up_2',
    'point_down',
    'point_right',
    'point_left',
    'raised_hands',
    'pray',
    'point_up',
    'clap',
    'muscle',
    'walking',
    'runner',
    'person_in_tuxedo',
    'dancer',
    'dancers',
    'ok_woman',
    'no_good',
    'information_desk_person',
    'bow',
    'couplekiss',
    'couple_with_heart',
    'massage',
    'haircut',
    'nail_care',
    'boy',
    'girl',
    'woman',
    'man',
    'baby',
    'older_woman',
    'older_man',
    'person_with_blond_hair',
    'man_with_gua_pi_mao',
    'man_with_turban',
    'construction_worker',
    'cop',
    'angel',
    'princess',
    'guardsman',
    'skull',
    'footprints',
    'kiss',
  ];
  // 按 Picker 顺序生成 emoji 列表
  const allEmojis: any[] = emojis
    .map((id) => {
      const e = (data as any).emojis[id];
      // 如果存在皮肤变体的 native 形式，取第一个；没有就直接取 emoji 本身
      return e?.skins?.[0]?.native ? e.skins[0].native : e;
    })
    .filter(Boolean);

  const [userFaces, setUserFaces] = useState<UserFace[]>(); // 收藏的表情包

  const { getPicInfo } = useFileMessage();

  const [currentGroupFaces, setCurrentGroupFaces] = useState<SysFace[]>(); // 当前表情包的表情

  const [currentStartGroupIndex, setCurrentStartGroupIndex] =
    useState<number>(0); // 当前开始的index范围

  // 切换表情包
  const handleFaceGroupSelected = async (faceGroupID: string) => {
    setSelectedGroupID(faceGroupID);

    if (faceGroupID === APPLE_EMOJI_TAB_KEY) {
      // setCurrentGroupFaces(data);
    } else if (faceGroupID !== MY_FAVOURITE_TAB_KEY) {
      const groupFaces = await IMSDK.getSysFaceGroup(faceGroupID);

      console.debug({ groupFaces });
      setCurrentGroupFaces(groupFaces?.data?.sysFaces || []);
    } else {
      setCurrentGroupFaces([]);
    }
  };

  // 选择表情，发送
  const handleEmojiClicked = async (item: UserFace | SysFace) => {
    try {
      setOpen(false);

      const createMsgRes = await IMSDK.createFaceMessage({
        faceURL: item.faceURL,
        faceName: item.faceName,
        faceWidth: item.faceWidth,
        faceHeight: item.faceHeight,
        faceSize: item.faceSize,
        imgType: item.imgType,
      });
      if (createMsgRes.data != null) {
        sendMessage({ message: createMsgRes.data });
      } else {
        message.error('发送失败');
      }
    } catch (e) {
      message.error('发送失败');
      console.error('发送表情失败', e);
    }
  };

  const initData = async () => {
    try {
      const resData = await IMSDK.getUserFaceGroup({ offset: 1, count: 200 });
      setFaceGroups([
        APPLE_EMOJI_TAB,
        MY_FAVOURITE_TAB,
        ...(resData?.data?.sysFaceGroupInfos || []),
      ]);
      setUserFaces(resData?.data?.userFaces || []);
    } catch (e) {
      console.error('initData', e);
    }
  };

  useEffect(() => {
    if (open) {
      initData();
    }
  }, [open]);

  const handleDeleteFace = async (faceID: string) => {
    try {
      const deleteRes = await IMSDK.deleteFace(faceID);
      console.debug({ deleteRes });

      setUserFaces((prev) => prev?.filter((item) => item.faceID !== faceID));
    } catch (e) {
      console.error('删除失败', e);
    }
  };

  const uploadCustomRequest = async (file: any) => {
    try {
      const isJpgOrPng =
        file.type === 'image/jpeg' ||
        file.type === 'image/png' ||
        file.type === 'image/jpg' ||
        file.type === 'image/gif';
      if (!isJpgOrPng) {
        message.error('请上传jpeg、png、jpg、gif格式的图片文件');
      }
      if (file.size > 2 * 1024 * 1024) {
        message.error('仅限选择2M以下的图片');
        return;
      }

      const uploadFileResult = await IMSDK.uploadFile({
        name: file.name,
        contentType: file.type,
        uuid: file.uuid,
        file,
      });
      const picInfo = await getPicInfo(file);
      await IMSDK.createFace({
        faceWidth: picInfo.width,
        faceHeight: picInfo.height,
        faceSize: file.size,
        imgType: file.type,
        faceURL: uploadFileResult.data.url,
        faceName: '自定义表情',
      });

      message.success('表情添加成功');
      initData();
    } catch (e) {
      if (e.errCode === 1801) {
        Message.error('此表情已存在，不可重复添加');
      } else {
        Message.error('添加至表情失败');
      }
    }
  };

  const total = faceGroups?.length ?? 0;
  const maxScrollIndex = Math.max(total - MAX_VISIBLE, 0);

  const isGotoLeftBtnValid = currentStartGroupIndex > 0;
  const isGotoRightBtnValid = currentStartGroupIndex < maxScrollIndex;

  const handleGotoLeft = () => {
    if (isGotoLeftBtnValid) {
      setCurrentStartGroupIndex((prev) => Math.max(prev - 1, 0));
    }
  };

  const handleGotoRight = () => {
    if (isGotoRightBtnValid) {
      setCurrentStartGroupIndex((prev) => Math.min(prev + 1, maxScrollIndex));
    }
  };

  useEffect(() => {
    emitter.on('REFRESHEMOJIS', initData);
    return () => {
      emitter.off('REFRESHEMOJIS', initData);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        popRef.current &&
        popRef.current.contains &&
        !popRef.current.contains(e.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <Popover
      ref={popRef}
      content={() => (
        <div className={styles.emojiComponentWrapContent}>
          <div className={styles.emojiNewEmojiTabContentArea}>
            {selectedGroupID === APPLE_EMOJI_TAB_KEY && (
              <div className={styles.emojiNewEmojiTabContent}>
                {allEmojis.map((emoji) => (
                  <div
                    className={styles.emoji}
                    key={emoji.id}
                    onClick={() => {
                      setOpen(false);
                      handleEmojiSelect(emoji);
                    }}
                  >
                    <div>{emoji}</div>
                  </div>
                ))}
              </div>
            )}
            {selectedGroupID === MY_FAVOURITE_TAB_KEY && (
              <div className={styles.emojiNewContent}>
                <div className={styles.emojiNew}>
                  <Upload
                    accept={'.jpg,.jpeg,.png,.gif'}
                    action={''}
                    customRequest={(file) => uploadCustomRequest(file.file)}
                    showUploadList={false}
                  >
                    <div
                      className={classNames(
                        styles.imgContainer,
                        styles.addFace
                      )}
                    >
                      <img src={addEmoji} />
                    </div>
                  </Upload>
                </div>
                {userFaces?.map((item) => {
                  return (
                    <Dropdown
                      key={item.faceID + item.faceURL}
                      trigger={['contextMenu']}
                      overlayClassName={styles.rightButtonMenu}
                      destroyPopupOnHide={true}
                      overlay={
                        <div
                          className={styles.userFaceMenu}
                          onClick={() => {
                            console.debug('触发删除');
                            handleDeleteFace(item.faceID);
                          }}
                        >
                          删除
                        </div>
                      }
                      placement="bottomRight"
                    >
                      <div
                        className={styles.emojiNew}
                        onClick={() => handleEmojiClicked(item)}
                      >
                        <div className={styles.imgContainer}>
                          <ImageWithLoading
                            src={item.faceURL}
                            loadingSrc={faceLoading}
                          />
                        </div>
                      </div>
                    </Dropdown>
                  );
                })}
              </div>
            )}
            {selectedGroupID !== MY_FAVOURITE_TAB_KEY &&
              selectedGroupID !== APPLE_EMOJI_TAB_KEY && (
                <div className={styles.emojiNewContent}>
                  {currentGroupFaces?.map((item) => (
                    <div
                      className={styles.emojiNew}
                      key={selectedGroupID + item.faceNo + item.faceURL}
                      onClick={() => handleEmojiClicked(item)}
                      title={item.faceName}
                    >
                      <div className={styles.imgContainer}>
                        <ImageWithLoading
                          src={item.thumbnailURL}
                          hoverSrc={item.faceURL}
                          loadingSrc={faceLoading}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
          </div>
          <div className={styles.emojiNewFooter}>
            <div
              className={classNames(
                styles.emojiNewFooterFaceIcon,
                isGotoLeftBtnValid ? '' : styles.emojiNewBtnUnValid,
                styles.emojiSwitchBtn
              )}
              onClick={handleGotoLeft}
            >
              <img src={isGotoLeftBtnValid ? goLeftValidIcon : goLeftIcon} />
            </div>
            <div className={styles.emojiNewFaceGroupsArea}>
              <div
                className={styles.emojiNewFaceGroups}
                style={{
                  transform: `translateX(${-56 * currentStartGroupIndex}px)`,
                }}
              >
                {faceGroups?.map((item) => (
                  <div
                    key={item.faceGroupID + item.faceGroupNo}
                    className={classNames(
                      styles.emojiNewFooterFaceIcon,
                      item.faceGroupID === selectedGroupID
                        ? styles.selected
                        : ''
                    )}
                    onClick={() => handleFaceGroupSelected(item.faceGroupID)}
                  >
                    {item.faceGroupID === APPLE_EMOJI_TAB_KEY ? (
                      <span>{item?.faceGroupIcon}</span>
                    ) : (
                      <img src={item.faceGroupIcon}></img>
                    )}
                  </div>
                ))}
              </div>
            </div>

            <div
              className={classNames(
                styles.emojiNewFooterFaceIcon,
                isGotoRightBtnValid ? '' : styles.emojiNewBtnUnValid,
                styles.emojiSwitchBtn
              )}
              onClick={handleGotoRight}
            >
              <img src={isGotoRightBtnValid ? goRightValidIcon : goRightIcon} />
            </div>
          </div>
        </div>
      )}
      placement="topLeft"
      mouseEnterDelay={0.3}
      trigger={'click'}
      open={open}
      onOpenChange={(val: boolean) => {
        if (disabledBtn) {
          setOpen(false);
        } else {
          if (val) {
            setIsToolTipOpen(false);
          }
          setOpen(val);
        }
      }}
      overlayClassName={styles.emojiComponentWarpNew}
    >
      <div
        onMouseDown={(e) => e.preventDefault()}
        className={classNames(
          styles.operateItem,
          disabledBtn && styles.operateItemdisabled
        )}
      >
        <Tooltip
          open={isToolTipOpen}
          onOpenChange={(val) => {
            if (open) {
              setIsToolTipOpen(false);
            } else {
              setIsToolTipOpen(val);
            }
          }}
          title={item.title ? item.title : null}
          overlayClassName={styles.tooltipWrap}
          overlayStyle={disabledBtn ? { display: 'none' } : {}}
        >
          <img src={item.icon} alt={item.title} />
        </Tooltip>
      </div>
    </Popover>
  );
};

export default EmojiComponentNew;
