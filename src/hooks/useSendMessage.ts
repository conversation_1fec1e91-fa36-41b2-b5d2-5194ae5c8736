/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */
import {
  CbEvents,
  MessageStatus,
  MessageType,
  ConversationItem,
} from '@ht/openim-wasm-client-sdk';
import { MessageItem } from '@ht/openim-wasm-client-sdk/lib/types/entity';
import { SendMsgParams } from '@ht/openim-wasm-client-sdk/lib/types/params';
import { useCallback, useState } from 'react';
import { IMSDK } from '@/layouts/BasicLayout';
import { getDefaultThreadConversation } from '@/utils/utils';
import { PromptModal } from '@/components/Channel/components/MessageListForeword/RobotConfigModal';
import { isEqual, throttle } from 'lodash';
import { useConversationStore, useUserStore } from '@/store';
import { shallow } from 'zustand/shallow';
import { playAudio } from '@/utils/tts';
import { emit } from '@/utils/events';
import { pushNewMessage, updateOneMessage } from './useHistoryMessageList';
import useThreadState from './useThreadState';

interface streamMessageProps {
  recvUser: any;
  curMsg: MessageItem;
  lastMsg?: MessageItem;
  curConversation?: ConversationItem;
}

interface msgToRead {
  content: string;
  hasRead: boolean;
}

// let sentences: string[] = [];
const break_symbols = ['。', '？', '\n'];
let reading = false;

export type SendMessageParams = Partial<Omit<SendMsgParams, 'message'>> & {
  message: MessageItem;
  needPush?: boolean;
  inRightThread?: boolean; // 闭包问题，新创建的thread框里，初始化时conversation传的值为空，第一次sendMessage拿不到更新后的currentThreadConversation，需要在函数里面查询
};
export function useSendMessage(conversation: any) {
  const { updateLlmLoading, updateLlmQuestions } = useConversationStore(
    (state) => ({
      updateLlmLoading: state.updateLlmLoading,
      updateLlmQuestions: state.updateLlmQuestions,
    }),
    shallow
  );

  const { syncState, connectState } = useUserStore(
    (state) => ({
      syncState: state.syncState,
      connectState: state.connectState,
    }),
    shallow
  );

  const showConnecting =
    syncState === 'success' &&
    (connectState === 'loading' || connectState === 'failed');

  const { updateBotConfigModal, botConfig, currentBotConfig } =
    useConversationStore(
      (state) => ({
        updateBotConfigModal: state.updateBotConfigModal,
        botConfig: state.botConfig,
        currentBotConfig: state.currentBotConfig,
      }),
      shallow
    );
  const { createThread, joinThread } = useThreadState();
  const throttledUpdateOneMessage = throttle(updateOneMessage, 50);
  const [sentences, setSentences] = useState<string[]>([]);

  const sendMessage = useCallback(
    async ({
      recvID,
      groupID,
      message,
      needPush,
      inRightThread,
    }: SendMessageParams) => {
      if (inRightThread && groupID == null) {
        console.error(
          'thread中初始化sendMessage失败',
          { inRightThread },
          { groupID }
        );
        return;
      }
      const currentConversation = inRightThread
        ? getDefaultThreadConversation(groupID as string)
        : conversation;

      if (conversation === null) {
        console.error(
          '发送消息失败，原因是：调用sendMessage时，conversation为空'
        );
        return;
      }
      const sourceID = recvID || groupID;
      const inCurrentConversation =
        currentConversation?.userID === sourceID ||
        currentConversation?.groupID === sourceID ||
        !sourceID;

      needPush = needPush ?? inCurrentConversation;
      if (needPush) {
        pushNewMessage({
          ...message,
          groupID: groupID ?? currentConversation?.groupID ?? '',
        });
      }

      const options = {
        recvID: recvID ?? currentConversation?.userID ?? '',
        groupID: groupID ?? currentConversation?.groupID ?? '',
        message,
      };

      try {
        const { data: successMessage } = await IMSDK.sendMessage(options);

        updateOneMessage(successMessage);
      } catch (error) {
        console.error('发消息失败了', error);
        updateOneMessage({
          ...message,
          status: MessageStatus.Failed,
        });
      }
    },
    [conversation]
  );

  // 右侧消息列发送消息
  const sendThreadMessage = async (
    message: MessageItem,
    inRightThread: boolean | undefined
  ) => {
    // 在已创建的thread中打开，点击发送
    if (conversation?.groupID != null) {
      const joinThreadResult = await joinThread(conversation?.groupID);
      if (joinThreadResult) {
        await sendMessage({ message });
      }
    } // 首次打开，需先创建thread
    else {
      const threadID = await createThread();

      if (threadID != null) {
        await sendMessage({
          message,
          groupID: threadID,
          inRightThread,
        });
      } else {
        console.error('创建thread失败，原因是threadID');
      }
    }
  };

  // 检查机器人配置是否有效，返回配置参数或undefined（表示配置无效）
  const checkBotConfig = useCallback(() => {
    if (botConfig && botConfig.length > 0) {
      const currentConfig = currentBotConfig?.config;
      const isRequired = botConfig.some((item) => item.required === 1);
      if (!currentConfig) {
        if (isRequired) {
          PromptModal({
            promptText: '请先填写对话变量，再开启对话',
            showFooterCancel: true,
            onOk: () => {
              updateBotConfigModal(true);
            },
            onOkText: '配置变量',
          });
          return undefined;
        }
      } else if (!isEqual(botConfig, currentConfig)) {
        PromptModal({
          promptText: '请先修改对话变量，再开启对话',
          showFooterCancel: true,
          onOk: () => {
            updateBotConfigModal(true, 'reset');
          },
          onOkText: '配置变量',
        });
        return undefined;
      }
      return currentBotConfig?.data || {};
    }
    return {};
  }, [botConfig, currentBotConfig, updateBotConfigModal]);

  const readSentence = () => {
    // debugger;
    console.log('sentences before', sentences);

    if (sentences.length === 0) {
      reading = false;
      return;
    }

    const currentSentence = sentences.shift();
    console.log('sentences after', sentences);

    reading = true;
    playAudio(currentSentence, readSentence);
  };

  const createStreamBuffer = () => {
    // 缓冲器状态
    const state = {
      queue: [] as { text: string; messageId: string }[],
      isProcessing: false,
      lastUpdateTime: 0,
      startTime: 0,
      renderedChars: 0,
      displayTexts: {} as Record<string, string>, // 按messageId存储已显示文本
      timer: null as NodeJS.Timeout | null,
      contents: {} as Record<string, any>, // 存储各消息的content
    };

    // 处理队列（合并所有文本后统一处理）
    const processQueue = () => {
      const now = performance.now();
      const elapsed = now - state.startTime;
      const expectedChars = Math.floor(elapsed / 5); // 5ms per char
      const pendingChars = expectedChars - state.renderedChars;

      if (pendingChars > 0 && state.queue.length > 0) {
        // 合并相同messageId的所有待渲染文本
        const currentMessageId = state.queue[0].messageId;
        const allText = state.queue
          .filter((item) => item.messageId === currentMessageId)
          .map((item) => item.text)
          .join('');

        // 计算本次要渲染的字符数（最多5个）
        const charsToRender = Math.min(pendingChars, allText.length, 5);
        const newText = allText.substring(0, charsToRender);

        // 更新显示文本
        state.displayTexts[currentMessageId] =
          (state.displayTexts[currentMessageId] || '') + newText;
        state.renderedChars += charsToRender;

        // 更新队列
        const remainingText = allText.slice(charsToRender);
        state.queue = state.queue.filter(
          (item) => item.messageId !== currentMessageId
        );
        if (remainingText.length > 0) {
          state.queue.unshift({
            text: remainingText,
            messageId: currentMessageId,
          });
        }

        state.lastUpdateTime = now;

        // 触发UI更新
        updateOneMessage({
          clientMsgID: currentMessageId,
          customElem: {
            data: JSON.stringify({
              type: 'stream',
              content: {
                ...state.contents[currentMessageId],
                answer: state.displayTexts[currentMessageId],
              },
            }),
          },
        } as MessageItem);
      }

      // 继续处理或停止
      if (state.queue.length > 0) {
        const nextDelay = Math.max(
          0,
          5 - (performance.now() - state.lastUpdateTime)
        );
        state.timer = setTimeout(processQueue, nextDelay);
      } else {
        state.isProcessing = false;
      }
    };

    return {
      append: (text: string, messageId: string, content: any) => {
        state.queue.push({ text, messageId });
        state.contents[messageId] = content;
        if (!state.isProcessing) {
          state.isProcessing = true;
          state.startTime = performance.now();
          state.lastUpdateTime = state.startTime;
          state.renderedChars = 0;
          processQueue();
        }
      },
      clear: (messageId: string) => {
        delete state.displayTexts[messageId];
        delete state.contents[messageId];
        state.queue = state.queue.filter(
          (item) => item.messageId !== messageId
        );
      },
      cleanup: () => {
        if (state.timer) {
          clearTimeout(state.timer);
        }
      },
    };
  };
  const sendStreamMessage = async ({
    recvUser,
    curMsg,
    lastMsg,
    curConversation,
  }: streamMessageProps) => {
    if (showConnecting) {
      return;
    }
    const parameters = checkBotConfig();
    if (parameters === undefined) {
      return;
    }
    updateLlmLoading(true);
    updateLlmQuestions([], '');
    // 用于存储流式消息的中间状态（zhangchen 0814 需放在sendMessage的作用域，不然过程数据中的plugin数据和think流式输出会被反复初始化）
    const streamMessageMap: Record<string, any> = {};
    const handleBotMessage = ({ data }: any) => {
      onReceiveMessage(data, streamMessageMap);
    };

    // 初始化缓冲器
    const streamBuffer = createStreamBuffer();

    const onReceiveMessage = async (
      data: any,
      streamMessageMap: Record<string, any>
    ) => {
      // 解析事件类型和message_id
      const { event } = data;
      const receiveData = JSON.parse(data.data);
      const messageId = receiveData.clientMsgId;
      if (!messageId) {
        return;
      }
      // 初始化或获取当前流式消息对象
      if (!streamMessageMap[messageId]) {
        streamMessageMap[messageId] = {
          type: 'stream',
          content: {
            id: messageId,
            answer: '',
            receiveData: receiveData.created_at || Date.now(),
          },
        };
      }
      const msg = streamMessageMap[messageId];
      const { content } = msg;

      // 处理不同事件
      switch (event) {
        case 'message_start':
          content.start = receiveData.created_at;
          streamBuffer.clear(messageId);
          break;
        case 'message':
          content.answer += receiveData.answer; // 原始完整内容
          streamBuffer.append(receiveData.answer, messageId, content); // 使用缓冲器
          return;

        case 'message_end':
          // playAudio(content.answer);
          // readSentence();
          content.end = receiveData.created_at;
          updateOneMessage({
            clientMsgID: receiveData.clientMsgId,
            customElem: {
              data: JSON.stringify({
                type: 'stream',
                content,
              }),
            },
          } as MessageItem);
          streamBuffer.clear(messageId); // 清理该消息的缓冲
          updateLlmLoading(false);
          emit('CHAT_LIST_SCROLL_TO_BOTTOM', {
            conversation: curConversation,
          });
          IMSDK.off(CbEvents.OnSendMessageToBot, handleBotMessage);
          return;
        case 'message_cost':
          content.input_tokens = receiveData.input_tokens;
          content.output_tokens = receiveData.output_tokens;
          content.latency = receiveData.latency;
          return;
        case 'think_message':
          if (typeof receiveData.answer === 'string') {
            content.think = {
              answer: (content.think?.answer || '') + receiveData.answer,
            };
          }
          break;
        case 'suggestion':
          updateLlmQuestions(receiveData.questions, receiveData.clientMsgId);
          break;
        case 'message_failed':
          content.error = {
            code: receiveData.code,
            msg: receiveData.error,
          };
          content.end = receiveData.created_at;
          break;

        case 'knowledge_retrieve_end':
          content.knowledge_retrieve = {
            ...content.knowledge_retrieve,
            end: receiveData.created_at,
            latency: receiveData.latency,
            results: receiveData.results,
          };
          break;
        // case 'qa_retrieve_end':
        //   content.qa_retrieve = {
        //     ...content.qa_retrieve,
        //     end: data.created_at,
        //     latency: data.latency,
        //     results: data.results,
        //   };
        //   break;
        // case 'network_search':
        //   content.network_search = content.network_search || {
        //     start: data.created_at,
        //     results: [],
        //   };
        //   break;
        case 'network_search_end':
          content.network_search = {
            ...content.network_search,
            end: receiveData.created_at,
            latency: receiveData.latency,
            results: receiveData.results,
          };
          break;
        case 'plugin_call':
        case 'plugin_call_end':
          // case 'terminology_retrieve':
          // case 'terminology_retrieve_end':
          // case 'tool_message_output_start':
          // case 'tool_message_output_end':
          // console.log('plugin_call, plugin_call_end', receiveData);
          // if (event !== 'plugin_call' && event !== 'plugin_call_end') {
          //   receiveData.event = event;
          // }
          // console.log('receiveData', receiveData);
          if (!content.plugins) {
            content.plugins = [receiveData];
          } else {
            const i = content.plugins.findIndex(
              (p) => p.index === receiveData.index
            );
            if (i === -1) {
              content.plugins.push(receiveData);
            } else {
              content.plugins[i] = receiveData;
            }
            // content.plugins.push(receiveData);
          }
          break;
        default:
          break;
      }

      if (
        content.answer || // 开始回答
        content.think?.answer || // 开始输出思考
        content.plugins?.length > 0 // 开始执行插件调用
      ) {
        throttledUpdateOneMessage({
          clientMsgID: receiveData.clientMsgId,
          customElem: {
            data: JSON.stringify({
              type: 'stream',
              content,
            }),
          },
        } as MessageItem);
      }
    };

    IMSDK.on(CbEvents.OnSendMessageToBot, handleBotMessage);
    await IMSDK.sendMsgToBotV2({
      recvID: recvUser.userID,
      message: lastMsg || curMsg,
      // receiverNickname: recvUser.nickname,
      subConversationID: conversation?.subConversationId || '',
      // 如果有上一条消息，说明是重新生成，那么seq使用当前消息的seq，否则使用0
      seq: lastMsg ? curMsg.seq : 0,
      parameters: parameters ? parameters : {},
    });
  };

  return {
    sendMessage,
    sendThreadMessage,
    sendStreamMessage,
  };
}
