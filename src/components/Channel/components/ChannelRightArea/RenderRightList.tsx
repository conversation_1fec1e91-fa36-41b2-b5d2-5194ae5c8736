import { memo } from 'react';
import { useConversationStore } from '@/store';
import { isEmpty } from 'lodash';
import UserDetail from '@/pages/contact/components/UserDetail';
import FilePreviewModal from '@/components/FilePreviewModal';
import RobotConversationList from '@/components/RobotConversation/RobotConversationList';
import RobotAnswerSource from '@/components/RobotConversation/RobotAnswerSource';
import RobotOnlineSearch from '@/components/RobotConversation/RobotOnlineSearch';
import ChannelHistory from '@/components/ChannelHistory';
import ChannelMemberList from '../ChannelMemberList';
import RightArea from '../RightArea';

const RenderRightList = ({
  ifShowMemberList,
  hasDeleteIcon
}: {
    ifShowMemberList: boolean;
    hasDeleteIcon: boolean;
}) => {
  const rightAreaInGroupConversation = useConversationStore(
    (state) => state.rightAreaInGroupConversation
  );
  const changeRightArea = useConversationStore(
    (state) => state.changeRightArea
  );
  const rightAreaInGroupList = useConversationStore(
    (state) => state.rightAreaInGroupList
  );

  if (isEmpty(rightAreaInGroupList)) {
    return ifShowMemberList && hasDeleteIcon ? <ChannelMemberList /> : null;
  } else {
    const newList = [...rightAreaInGroupList];
    let defaultWidth = '504px';
    let domId = 'drag-scroll-bar-right';
    let needLocalStorageWidth = true;
    if (
      rightAreaInGroupConversation &&
      ['personDetail', 'file', 'channelHistory'].includes(
        rightAreaInGroupConversation
      )
    ) {
      defaultWidth = '560px';
      domId = 'drag-scroll-bar-right-channelHistory';
      needLocalStorageWidth = false;
    }
    return (
      <RightArea
        defaultWidth={defaultWidth}
        domId={domId}
        needLocalStorageWidth={needLocalStorageWidth}
      >
        {newList.reverse().map((i: any) => {
          return (
            <div key={i.key} style={{ height: '100%' }}>
              {i.type === 'personDetail' && (
                <UserDetail
                  userID={i.payload}
                  handleSelectedUserClear={() => {
                    changeRightArea('CLEAR_RIGHT_AREA');
                  }}
                />
              )}
              {i.type === 'file' && (
                <FilePreviewModal
                  message={i.payload}
                  onClose={() => {
                    changeRightArea('CLEAR_RIGHT_AREA');
                  }}
                />
              )}
              {i.type === 'robotConversation' && (
                <RobotConversationList
                  onClose={() => {
                    changeRightArea('CLEAR_RIGHT_AREA');
                  }}
                />
              )}
              {i.type === 'robotAnswerSource' && (
                <RobotAnswerSource
                  data={i.payload?.data || []}
                  activeValue={i.payload?.activeObj || undefined}
                  onClose={() => {
                    changeRightArea('CLEAR_RIGHT_AREA');
                  }}
                />
              )}
              {i.type === 'robotOnlineSearch' && (
                <RobotOnlineSearch
                  data={i.payload?.data || []}
                  activeValue={i.payload?.activeObj || undefined}
                  onClose={() => {
                    changeRightArea('CLEAR_RIGHT_AREA');
                  }}
                />
              )}
              {i.type === 'channelHistory' && <ChannelHistory />}
            </div>
          );
        })}
      </RightArea>
    );
  }
};

export default memo(RenderRightList);
