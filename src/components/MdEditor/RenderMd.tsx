/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable @typescript-eslint/restrict-template-expressions */
/* eslint-disable react/no-danger */
/*
 * @Author: ypt
 * @LastEditors: ypt
 * @LastEditTime: 2025-03-12 11:18:17
 * @FilePath: /linkflow/src/components/MdEditor/RenderMd.tsx
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { throttle } from 'lodash';
import classNames from 'classnames';
import {
  parserMdToHtml,
  processEscapeCharacters,
} from '@/utils/parserMdToHtml';
import { copyContent } from '@/utils/message';

import imgIcon from '@/assets/md/imgIcon.png';

import styles from './index.less';
import ImgPreviewModal from '../ImgPreviewModal';

interface Iprops {
  value: string;
  isQuote?: boolean;
  id: string;
  getSize?: (size: DOMRect) => void;
  isSender?: boolean;
  hasKnowledgeRetrieve?: boolean;
  hasNetworkSearch?: boolean;
  referenceClick?: (val: number, type: 'know' | 'network') => void;
}
// 创建alt文字元素
const createAltElement = (altText: string) => {
  const altElement = document.createElement('div');
  altElement.style.position = 'absolute';
  altElement.style.top = '0px';
  altElement.style.left = '0px';
  altElement.style.color = 'var(--primary-text-color-7)';
  altElement.style.fontSize = '12px';
  altElement.style.backgroundColor = 'var(--primary-background-color-8)';
  altElement.style.padding = '2px 6px';
  altElement.style.borderRadius = '8px 0px 8px 0px';
  altElement.innerText = altText;
  return altElement;
};

// 创建图标元素
const createIconElement = () => {
  const iconElement = document.createElement('img');
  iconElement.src = imgIcon;
  iconElement.style.width = '48px';
  iconElement.style.height = '48px';
  iconElement.style.marginBottom = '8px';
  iconElement.style.opacity = '0.6';
  return iconElement;
};

// 创建文字元素
const createTextElement = () => {
  const textElement = document.createElement('div');
  textElement.style.color = 'var(--primary-text-color-7)';
  textElement.style.fontSize = '13px';
  textElement.style.textAlign = 'center';
  textElement.innerText = '无法查看此图片';
  return textElement;
};

// 创建图片加载失败的占位符
const createImageErrorPlaceholder = (altText: string) => {
  const errorPlaceholder = document.createElement('div');
  errorPlaceholder.className = 'image-load-error';
  errorPlaceholder.style.width = '200px';
  errorPlaceholder.style.height = '100px';
  errorPlaceholder.style.border = '1px solid var(--primary-background-color-5)';
  errorPlaceholder.style.borderRadius = '8px';
  errorPlaceholder.style.position = 'relative';
  errorPlaceholder.style.display = 'flex';
  errorPlaceholder.style.flexDirection = 'column';
  errorPlaceholder.style.alignItems = 'center';
  errorPlaceholder.style.justifyContent = 'center';
  errorPlaceholder.style.padding = '20px';
  errorPlaceholder.style.boxSizing = 'border-box';

  if (altText) {
    errorPlaceholder.appendChild(createAltElement(altText));
  }

  errorPlaceholder.appendChild(createIconElement());
  errorPlaceholder.appendChild(createTextElement());

  return errorPlaceholder;
};

export const RenderMd = ({
  value,
  isQuote = false,
  id,
  getSize,
  isSender = false,
  hasKnowledgeRetrieve = false,
  hasNetworkSearch = false,
  referenceClick,
}: Iprops) => {
  const mdRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (mdRef.current) {
      const imgElements = mdRef.current.querySelectorAll('img');
      imgElements.forEach((img) => {
        // 设置超时检测（10秒未加载则触发 error）
        const timeoutId = setTimeout(() => {
          if (!img.hasAttribute('load-success')) {
            img.dispatchEvent(new Event('error'));
          }
        }, 10000); // 10秒超时

        // 监听加载事件
        img.addEventListener('load', () => {
          clearTimeout(timeoutId);
          // 添加加载成功属性
          img.setAttribute('load-success', 'true');
        });

        // 监听加载失败事件
        img.addEventListener('error', () => {
          clearTimeout(timeoutId);
          // 获取图片的alt属性
          const altText = img.getAttribute('alt') || '';
          // 创建错误占位符
          const errorPlaceholder = createImageErrorPlaceholder(altText);
          if (img.parentNode) {
            img.parentNode.replaceChild(errorPlaceholder, img);
          }
        });

        return () => {
          clearTimeout(timeoutId);
        };
      });
    }
  }, [value]);

  // 记录开始时间
  const parseStart = performance.now();
  console.debug('renderMd--1', value);
  // 使用dangerouslySetInnerHTML直接渲染HTML内容
  const htmlContent = {
    __html: parserMdToHtml(value, {
      hasKnowledgeRetrieve,
      hasNetworkSearch,
    }),
  };
  // 记录结束时间
  const parseEnd = performance.now();
  console.debug('renderMd--2', htmlContent, parseEnd);
  console.debug('parseMdToHtml cost', parseEnd - parseStart, 'ms', {
    parseStart,
    parseEnd,
  });

  // 处理参考文献点击事件
  const handleClick = (e: React.MouseEvent<HTMLDivElement>) => {
    handleReferenceClick(e);
    handleImgClickThrottle(e);
    handleCopyCodeClick(e);
  };
  const handleReferenceClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (hasKnowledgeRetrieve && e.target instanceof HTMLElement) {
      const button = e.target.closest('.knowledge-retrieve');
      if (button) {
        e.preventDefault();
        const referenceNumber = button.getAttribute('data-reference');
        // 可以在这里添加跳转到参考文献位置或显示参考文献详情的逻辑
        referenceNumber && referenceClick?.(Number(referenceNumber), 'know');
      }
    }
    if (hasNetworkSearch && e.target instanceof HTMLElement) {
      const button = e.target.closest('.network-search');
      if (button) {
        e.preventDefault();
        const referenceNumber = button.getAttribute('data-reference');
        // 可以在这里添加跳转到参考文献位置或显示参考文献详情的逻辑
        referenceNumber && referenceClick?.(Number(referenceNumber), 'network');
      }
    }
  };

  const handleImgClick = useCallback(
    async (e: React.MouseEvent<HTMLDivElement>) => {
      // 从e.target中获取图片的src属性且load-success属性为true
      const imgSrc =
        (e.target as HTMLImageElement).getAttribute('load-success') ===
          'true' && (e.target as HTMLImageElement).src;
      if (imgSrc) {
        ImgPreviewModal.open({
          url: imgSrc,
        });
      }
    },
    []
  );

  const handleImgClickThrottle = throttle(handleImgClick, 300, {
    leading: false,
    trailing: true,
  });

  const handleCopyCodeClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // 检查点击的是否是复制按钮
    if (
      e.target instanceof HTMLElement &&
      e.target.closest('.copy-button-click')
    ) {
      const copyButton = e.target.closest('.copy-button-click');
      if (copyButton) {
        // 获取按钮下的pre标签
        const hiddenPre = copyButton.querySelector('pre');
        if (hiddenPre) {
          // 将这个hiddenPre的style的display属性设置为block
          const originalDisplay = hiddenPre.style.display;
          hiddenPre.style.display = 'block';

          // 复制内容
          copyContent(hiddenPre.outerHTML);

          // 恢复原始display状态
          hiddenPre.style.display = originalDisplay;
        }
      }
    }
  };

  return (
    <div>
      <div
        ref={mdRef}
        className={classNames(isSender ? styles.senWarp : '', styles.mdWrap)}
      >
        <div className="milkdown">
          <div
            className="ProseMirror"
            dangerouslySetInnerHTML={htmlContent}
            onClick={handleClick}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default RenderMd;
